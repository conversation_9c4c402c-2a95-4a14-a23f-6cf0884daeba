"""
Database Manager - Handles all database operations
"""
import sqlite3
import threading
from contextlib import contextmanager
from typing import List, Dict, Any, Optional
from config.settings import settings

class DatabaseManager:
    """Thread-safe database manager"""
    
    def __init__(self):
        self.db_path = settings.database_path
        self._local = threading.local()
    
    def _get_connection(self):
        """Get thread-local database connection"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # Enable foreign key constraints
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        return self._local.connection
    
    @contextmanager
    def get_cursor(self, commit=True):
        """Context manager for database operations"""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            if commit:
                conn.commit()
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a SELECT query and return results"""
        with self.get_cursor(commit=False) as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            return results
    
    def execute_non_query(self, query: str, params: tuple = None) -> int:
        """Execute INSERT, UPDATE, DELETE query and return affected rows"""
        with self.get_cursor() as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.rowcount
    
    def execute_scalar(self, query: str, params: tuple = None) -> Any:
        """Execute query and return single value"""
        with self.get_cursor(commit=False) as cursor:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            result = cursor.fetchone()
            return result[0] if result else None
    
    def insert_record(self, table: str, data: Dict[str, Any]) -> int:
        """Insert a record and return the ID"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        with self.get_cursor() as cursor:
            cursor.execute(query, tuple(data.values()))
            return cursor.lastrowid
    
    def update_record(self, table: str, data: Dict[str, Any], where_clause: str, where_params: tuple = None) -> int:
        """Update records and return affected count"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
        
        params = list(data.values())
        if where_params:
            params.extend(where_params)
        
        return self.execute_non_query(query, tuple(params))
    
    def delete_record(self, table: str, where_clause: str, where_params: tuple = None) -> int:
        """Delete records and return affected count"""
        query = f"DELETE FROM {table} WHERE {where_clause}"
        return self.execute_non_query(query, where_params)
    
    def get_record_by_id(self, table: str, record_id: int) -> Optional[Dict]:
        """Get a single record by ID"""
        query = f"SELECT * FROM {table} WHERE id = ?"
        results = self.execute_query(query, (record_id,))
        return results[0] if results else None
    
    def get_records(self, table: str, where_clause: str = None, where_params: tuple = None, 
                   order_by: str = None, limit: int = None) -> List[Dict]:
        """Get multiple records with optional filtering"""
        query = f"SELECT * FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, where_params)
    
    def record_exists(self, table: str, where_clause: str, where_params: tuple = None) -> bool:
        """Check if a record exists"""
        query = f"SELECT 1 FROM {table} WHERE {where_clause} LIMIT 1"
        result = self.execute_scalar(query, where_params)
        return result is not None
    
    def get_next_sequence(self, table: str, field: str, prefix: str = "") -> str:
        """Generate next sequence number for a field"""
        query = f"SELECT MAX(CAST(SUBSTR({field}, {len(prefix) + 1}) AS INTEGER)) FROM {table}"
        if prefix:
            query += f" WHERE {field} LIKE '{prefix}%'"
        
        max_num = self.execute_scalar(query)
        next_num = (max_num or 0) + 1
        return f"{prefix}{next_num:06d}"
    
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database"""
        try:
            with sqlite3.connect(backup_path) as backup_conn:
                self._get_connection().backup(backup_conn)
            return True
        except Exception as e:
            print(f"Backup failed: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """Restore database from backup"""
        try:
            # Close current connection
            if hasattr(self._local, 'connection'):
                self._local.connection.close()
                delattr(self._local, 'connection')
            
            # Copy backup to main database
            with sqlite3.connect(backup_path) as backup_conn:
                with sqlite3.connect(self.db_path) as main_conn:
                    backup_conn.backup(main_conn)
            
            return True
        except Exception as e:
            print(f"Restore failed: {e}")
            return False
    
    def close_connection(self):
        """Close the database connection"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')

# Global database manager instance
db_manager = DatabaseManager()
