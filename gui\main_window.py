"""
Main Application Window - Primary interface after login
"""
import customtkinter as ctk
from tkinter import messagebox
from core.auth import auth_manager
from core.localization import localization_manager, _

class MainWindow:
    """Main application window"""
    
    def __init__(self, user_info, logout_callback=None):
        self.window = None
        self.user_info = user_info
        self.logout_callback = logout_callback
        
        # UI elements that need updating
        self.welcome_label = None
        self.logout_button = None
        self.language_menu = None
        self.language_var = None
        
        # Set appearance mode and color theme
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
    
    def create_window(self):
        """Create and display the main window"""
        # Create main window
        self.window = ctk.CTk()
        self.window.title(_("app.title", "Business Management System"))
        self.window.geometry("1200x800")
        
        # Center the window
        self.center_window()
        
        # Create UI elements
        self.create_ui()
        
        # Configure for RTL if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        return self.window
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_ui(self):
        """Create the user interface elements"""
        # Main container
        main_container = ctk.CTkFrame(self.window)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Header frame
        header_frame = ctk.CTkFrame(main_container)
        header_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        # Title and user info
        title_frame = ctk.CTkFrame(header_frame)
        title_frame.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        
        app_title = ctk.CTkLabel(
            title_frame,
            text=_("app.title", "Business Management System"),
            font=ctk.CTkFont(size=20, weight="bold")
        )
        app_title.pack(pady=(10, 5))
        
        self.welcome_label = ctk.CTkLabel(
            title_frame,
            text=f"{_('login.welcome', 'Welcome back!')}\n{self.user_info['first_name']} {self.user_info['last_name']}",
            font=ctk.CTkFont(size=14)
        )
        self.welcome_label.pack(pady=(0, 10))
        
        # Controls frame (language and logout)
        controls_frame = ctk.CTkFrame(header_frame)
        controls_frame.pack(side="right", padx=10, pady=10)
        
        # Language selection
        language_label = ctk.CTkLabel(
            controls_frame,
            text=_("settings.language", "Language"),
            font=ctk.CTkFont(size=12)
        )
        language_label.pack(pady=(10, 5))
        
        self.language_var = ctk.StringVar(value=localization_manager.current_language)
        self.language_menu = ctk.CTkOptionMenu(
            controls_frame,
            variable=self.language_var,
            values=list(localization_manager.get_available_languages().keys()),
            command=self.on_language_change,
            width=100
        )
        self.language_menu.pack(pady=(0, 10))
        
        # Logout button
        self.logout_button = ctk.CTkButton(
            controls_frame,
            text=_("navigation.logout", "Logout"),
            command=self.on_logout_click,
            font=ctk.CTkFont(size=12),
            width=100
        )
        self.logout_button.pack(pady=(0, 10))
        
        # Content area
        content_frame = ctk.CTkFrame(main_container)
        content_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Dashboard content (placeholder for now)
        dashboard_label = ctk.CTkLabel(
            content_frame,
            text=_("dashboard.welcome_message", "Welcome to Business Management System"),
            font=ctk.CTkFont(size=16)
        )
        dashboard_label.pack(expand=True)
        
        # Status message
        status_label = ctk.CTkLabel(
            content_frame,
            text="🚧 Business modules will be implemented in the next development phase",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        status_label.pack(pady=20)
    
    def configure_rtl_layout(self):
        """Configure layout for right-to-left languages"""
        # This would involve adjusting text alignment and layout direction
        # For now, we'll keep the basic layout but this can be enhanced
        pass
    
    def on_language_change(self, selected_language):
        """Handle language change"""
        localization_manager.set_language(selected_language)
        self.refresh_ui()
    
    def refresh_ui(self):
        """Refresh UI with new language without destroying the window"""
        if not self.window:
            return
        
        # Update window title
        self.window.title(_("app.title", "Business Management System"))
        
        # Update all text elements
        self.update_widget_texts(self.window)
        
        # Update specific elements that we have references to
        if self.welcome_label:
            self.welcome_label.configure(
                text=f"{_('login.welcome', 'Welcome back!')}\n{self.user_info['first_name']} {self.user_info['last_name']}"
            )
        
        if self.logout_button:
            self.logout_button.configure(text=_("navigation.logout", "Logout"))
        
        # Configure RTL layout if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()
    
    def update_widget_texts(self, widget):
        """Recursively update text in all widgets"""
        try:
            widget_class = widget.__class__.__name__
            
            if widget_class == "CTkLabel":
                current_text = widget.cget("text")
                if "Business Management System" in current_text or "نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("app.title", "Business Management System"))
                elif "Language" in current_text or "اللغة" in current_text:
                    widget.configure(text=_("settings.language", "Language"))
                elif "Welcome to Business Management System" in current_text or "مرحباً بك في نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("dashboard.welcome_message", "Welcome to Business Management System"))
            
            elif widget_class == "CTkButton":
                current_text = widget.cget("text")
                if "Logout" in current_text or "تسجيل الخروج" in current_text:
                    widget.configure(text=_("navigation.logout", "Logout"))
            
            # Recursively update child widgets
            try:
                for child in widget.winfo_children():
                    self.update_widget_texts(child)
            except:
                pass
                
        except Exception as e:
            pass
    
    def on_logout_click(self):
        """Handle logout button click"""
        if messagebox.askyesno(
            _("navigation.logout", "Logout"),
            "Are you sure you want to logout?"
        ):
            if self.logout_callback:
                self.logout_callback()
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno(
            "Exit Application",
            "Are you sure you want to exit?"
        ):
            if self.logout_callback:
                self.logout_callback()
    
    def show(self):
        """Show the main window"""
        if self.window:
            self.window.mainloop()
    
    def destroy(self):
        """Destroy the main window"""
        if self.window:
            self.window.destroy()
