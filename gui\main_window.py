"""
Main Application Window - Primary interface after login
"""
import customtkinter as ctk
from tkinter import messagebox
from core.auth import auth_manager
from core.localization import localization_manager, _

class MainWindow:
    """Main application window"""
    
    def __init__(self, user_info, logout_callback=None):
        self.window = None
        self.user_info = user_info
        self.logout_callback = logout_callback
        
        # UI elements that need updating
        self.welcome_label = None
        self.logout_button = None
        self.language_menu = None
        self.language_var = None
        
        # Set professional appearance mode and color theme
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # Professional color scheme
        self.colors = {
            'primary': '#1f538d',      # Professional blue
            'secondary': '#2c5aa0',    # Lighter blue
            'accent': '#e8f4fd',       # Light blue accent
            'success': '#28a745',      # Green
            'warning': '#ffc107',      # Amber
            'danger': '#dc3545',       # Red
            'dark': '#343a40',         # Dark gray
            'light': '#f8f9fa',        # Light gray
            'white': '#ffffff',        # Pure white
            'border': '#dee2e6',       # Border gray
            'text_primary': '#212529', # Primary text
            'text_secondary': '#6c757d' # Secondary text
        }
    
    def create_window(self):
        """Create and display the main window"""
        # Create main window
        self.window = ctk.CTk()
        self.window.title(_("app.title", "Business Management System"))

        # Start with a reasonable size before maximizing
        self.window.geometry("1200x800")

        # Maximize the window (cross-platform approach)
        self.maximize_window()

        # Create UI elements
        self.create_ui()

        # Configure for RTL if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()

        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        return self.window

    def maximize_window(self):
        """Maximize the window across different operating systems"""
        import platform

        system = platform.system().lower()

        try:
            if system == "windows":
                # Windows
                self.window.state('zoomed')
            elif system == "darwin":
                # macOS
                self.window.attributes('-zoomed', True)
            else:
                # Linux and others
                try:
                    self.window.attributes('-zoomed', True)
                except:
                    # Fallback: manually set to screen size
                    self.window.update_idletasks()
                    width = self.window.winfo_screenwidth()
                    height = self.window.winfo_screenheight()
                    self.window.geometry(f"{width}x{height}+0+0")
        except Exception as e:
            # Fallback: set to a large size if maximization fails
            print(f"Could not maximize window: {e}")
            self.window.geometry("1400x900")

    def create_ui(self):
        """Create the professional user interface"""
        # Remove default padding and create full-screen layout
        self.window.configure(fg_color=self.colors['light'])

        # Create main layout with professional structure
        self.create_header()
        self.create_sidebar()
        self.create_main_content()
        self.create_status_bar()

    def create_header(self):
        """Create professional header with branding and user controls"""
        # Header container
        header_frame = ctk.CTkFrame(
            self.window,
            height=80,
            fg_color=self.colors['primary'],
            corner_radius=0
        )
        header_frame.pack(fill="x", side="top")
        header_frame.pack_propagate(False)

        # Left side - Logo and title
        left_header = ctk.CTkFrame(header_frame, fg_color="transparent")
        left_header.pack(side="left", fill="y", padx=20, pady=10)

        # Company logo placeholder (you can add actual logo later)
        logo_frame = ctk.CTkFrame(
            left_header,
            width=50,
            height=50,
            fg_color=self.colors['white'],
            corner_radius=8
        )
        logo_frame.pack(side="left", padx=(0, 15))
        logo_frame.pack_propagate(False)

        logo_label = ctk.CTkLabel(
            logo_frame,
            text="BMS",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['primary']
        )
        logo_label.pack(expand=True)

        # Title and subtitle
        title_container = ctk.CTkFrame(left_header, fg_color="transparent")
        title_container.pack(side="left", fill="y")

        app_title = ctk.CTkLabel(
            title_container,
            text=_("app.title", "Business Management System"),
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.colors['white']
        )
        app_title.pack(anchor="w", pady=(8, 0))

        subtitle = ctk.CTkLabel(
            title_container,
            text="Professional Business Solutions",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['accent']
        )
        subtitle.pack(anchor="w")

        # Right side - User info and controls
        right_header = ctk.CTkFrame(header_frame, fg_color="transparent")
        right_header.pack(side="right", fill="y", padx=20, pady=10)

        # User info
        user_frame = ctk.CTkFrame(
            right_header,
            fg_color=self.colors['secondary'],
            corner_radius=8
        )
        user_frame.pack(side="right", padx=(0, 15), fill="y")

        self.welcome_label = ctk.CTkLabel(
            user_frame,
            text=f"{self.user_info['first_name']} {self.user_info['last_name']}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=self.colors['white']
        )
        self.welcome_label.pack(padx=15, pady=(8, 2))

        role_label = ctk.CTkLabel(
            user_frame,
            text=f"Role: {self.user_info.get('role_name', 'User')}",
            font=ctk.CTkFont(size=11),
            text_color=self.colors['accent']
        )
        role_label.pack(padx=15, pady=(0, 8))

        # Controls container
        controls_container = ctk.CTkFrame(right_header, fg_color="transparent")
        controls_container.pack(side="right", fill="y")

        # Language selection
        lang_frame = ctk.CTkFrame(controls_container, fg_color="transparent")
        lang_frame.pack(pady=(0, 5))

        self.language_var = ctk.StringVar(value=localization_manager.current_language)
        self.language_menu = ctk.CTkOptionMenu(
            lang_frame,
            variable=self.language_var,
            values=list(localization_manager.get_available_languages().keys()),
            command=self.on_language_change,
            width=80,
            height=28,
            font=ctk.CTkFont(size=11),
            fg_color=self.colors['white'],
            button_color=self.colors['secondary'],
            text_color=self.colors['dark']
        )
        self.language_menu.pack()

        # Logout button
        self.logout_button = ctk.CTkButton(
            controls_container,
            text=_("navigation.logout", "Logout"),
            command=self.on_logout_click,
            width=80,
            height=28,
            font=ctk.CTkFont(size=11, weight="bold"),
            fg_color=self.colors['danger'],
            hover_color="#c82333"
        )
        self.logout_button.pack()

    def create_sidebar(self):
        """Create professional sidebar navigation"""
        # Sidebar container
        self.sidebar = ctk.CTkFrame(
            self.window,
            width=250,
            fg_color=self.colors['dark'],
            corner_radius=0
        )
        self.sidebar.pack(side="left", fill="y")
        self.sidebar.pack_propagate(False)

        # Navigation title
        nav_title = ctk.CTkLabel(
            self.sidebar,
            text=_("navigation.dashboard", "Navigation"),
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['white']
        )
        nav_title.pack(pady=(20, 10), padx=20, anchor="w")

        # Navigation items
        nav_items = [
            ("dashboard", "Dashboard", "📊"),
            ("customers", "Customers", "👥"),
            ("suppliers", "Suppliers", "🏢"),
            ("inventory", "Inventory", "📦"),
            ("purchases", "Purchases", "🛒"),
            ("sales", "Sales", "💰"),
            ("invoices", "Invoices", "📄"),
            ("accounting", "Accounting", "📈"),
            ("reports", "Reports", "📋"),
            ("employees", "Employees", "👤"),
            ("settings", "Settings", "⚙️")
        ]

        self.nav_buttons = {}
        for key, label, icon in nav_items:
            btn = ctk.CTkButton(
                self.sidebar,
                text=f"{icon}  {_(f'navigation.{key}', label)}",
                command=lambda k=key: self.navigate_to(k),
                width=210,
                height=40,
                font=ctk.CTkFont(size=13),
                fg_color="transparent",
                text_color=self.colors['light'],
                hover_color=self.colors['secondary'],
                anchor="w"
            )
            btn.pack(pady=2, padx=20, fill="x")
            self.nav_buttons[key] = btn

        # Set dashboard as active
        self.set_active_nav("dashboard")

    def create_main_content(self):
        """Create main content area"""
        # Main content container
        self.main_content = ctk.CTkFrame(
            self.window,
            fg_color=self.colors['white'],
            corner_radius=0
        )
        self.main_content.pack(side="right", fill="both", expand=True)

        # Content header
        content_header = ctk.CTkFrame(
            self.main_content,
            height=60,
            fg_color=self.colors['light'],
            corner_radius=0
        )
        content_header.pack(fill="x", padx=0, pady=0)
        content_header.pack_propagate(False)

        # Page title
        self.page_title = ctk.CTkLabel(
            content_header,
            text=_("dashboard.title", "Dashboard"),
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['dark']
        )
        self.page_title.pack(side="left", padx=30, pady=15)

        # Breadcrumb
        self.breadcrumb = ctk.CTkLabel(
            content_header,
            text="Home > Dashboard",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        self.breadcrumb.pack(side="left", padx=(0, 30), pady=15)

        # Main content area
        self.content_area = ctk.CTkScrollableFrame(
            self.main_content,
            fg_color=self.colors['white']
        )
        self.content_area.pack(fill="both", expand=True, padx=30, pady=(20, 30))

        # Load dashboard content
        self.load_dashboard_content()

    def create_status_bar(self):
        """Create professional status bar"""
        status_bar = ctk.CTkFrame(
            self.window,
            height=30,
            fg_color=self.colors['border'],
            corner_radius=0
        )
        status_bar.pack(fill="x", side="bottom")
        status_bar.pack_propagate(False)

        # Status info
        status_left = ctk.CTkLabel(
            status_bar,
            text="Ready",
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_secondary']
        )
        status_left.pack(side="left", padx=20, pady=5)

        # Version info
        status_right = ctk.CTkLabel(
            status_bar,
            text=_("app.version", "Version 1.0.0"),
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_secondary']
        )
        status_right.pack(side="right", padx=20, pady=5)

    def load_dashboard_content(self):
        """Load professional dashboard content"""
        # Welcome section
        welcome_frame = ctk.CTkFrame(
            self.content_area,
            fg_color=self.colors['accent'],
            corner_radius=12
        )
        welcome_frame.pack(fill="x", pady=(0, 20))

        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text=f"{_('login.welcome', 'Welcome back!')}, {self.user_info['first_name']}!",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.colors['primary']
        )
        welcome_title.pack(pady=(20, 5), padx=30, anchor="w")

        welcome_subtitle = ctk.CTkLabel(
            welcome_frame,
            text=_("dashboard.welcome_message", "Here's what's happening with your business today."),
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        welcome_subtitle.pack(pady=(0, 20), padx=30, anchor="w")

        # KPI Cards
        kpi_frame = ctk.CTkFrame(self.content_area, fg_color="transparent")
        kpi_frame.pack(fill="x", pady=(0, 20))

        # Configure grid
        kpi_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)

        kpi_data = [
            ("Total Sales", "$12,450", "↗ +12%", self.colors['success']),
            ("Pending Orders", "23", "↗ +5%", self.colors['warning']),
            ("Low Stock Items", "8", "↓ -2%", self.colors['danger']),
            ("Active Customers", "156", "↗ +8%", self.colors['primary'])
        ]

        for i, (title, value, change, color) in enumerate(kpi_data):
            self.create_kpi_card(kpi_frame, title, value, change, color, i)

        # Quick Actions
        actions_frame = ctk.CTkFrame(
            self.content_area,
            fg_color=self.colors['light'],
            corner_radius=12
        )
        actions_frame.pack(fill="x", pady=(0, 20))

        actions_title = ctk.CTkLabel(
            actions_frame,
            text="Quick Actions",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['dark']
        )
        actions_title.pack(pady=(20, 10), padx=30, anchor="w")

        # Action buttons
        actions_container = ctk.CTkFrame(actions_frame, fg_color="transparent")
        actions_container.pack(fill="x", padx=30, pady=(0, 20))

        quick_actions = [
            ("New Sale", "💰", "sales"),
            ("Add Customer", "👥", "customers"),
            ("Create Invoice", "📄", "invoices"),
            ("Check Inventory", "📦", "inventory")
        ]

        for i, (text, icon, nav_key) in enumerate(quick_actions):
            btn = ctk.CTkButton(
                actions_container,
                text=f"{icon} {text}",
                command=lambda k=nav_key: self.navigate_to(k),
                width=180,
                height=50,
                font=ctk.CTkFont(size=13, weight="bold"),
                fg_color=self.colors['primary'],
                hover_color=self.colors['secondary']
            )
            btn.pack(side="left", padx=(0, 15) if i < len(quick_actions)-1 else 0)

        # Development status
        dev_frame = ctk.CTkFrame(
            self.content_area,
            fg_color=self.colors['warning'],
            corner_radius=12
        )
        dev_frame.pack(fill="x", pady=(0, 20))

        dev_title = ctk.CTkLabel(
            dev_frame,
            text="🚧 Development Status",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['white']
        )
        dev_title.pack(pady=(15, 5), padx=30, anchor="w")

        dev_text = ctk.CTkLabel(
            dev_frame,
            text="Business modules are ready for implementation. The foundation is complete with authentication, database, and professional UI framework.",
            font=ctk.CTkFont(size=12),
            text_color=self.colors['white'],
            wraplength=800
        )
        dev_text.pack(pady=(0, 15), padx=30, anchor="w")

    def create_kpi_card(self, parent, title, value, change, color, column):
        """Create a professional KPI card"""
        card = ctk.CTkFrame(
            parent,
            fg_color=self.colors['white'],
            corner_radius=12,
            border_width=1,
            border_color=self.colors['border']
        )
        card.grid(row=0, column=column, padx=10, pady=10, sticky="ew")

        # Title
        title_label = ctk.CTkLabel(
            card,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        title_label.pack(pady=(15, 5), padx=20, anchor="w")

        # Value
        value_label = ctk.CTkLabel(
            card,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['dark']
        )
        value_label.pack(pady=(0, 5), padx=20, anchor="w")

        # Change indicator
        change_label = ctk.CTkLabel(
            card,
            text=change,
            font=ctk.CTkFont(size=11, weight="bold"),
            text_color=color
        )
        change_label.pack(pady=(0, 15), padx=20, anchor="w")

    def navigate_to(self, section):
        """Navigate to different sections"""
        # Update active navigation
        self.set_active_nav(section)

        # Update page title and breadcrumb
        section_titles = {
            "dashboard": "Dashboard",
            "customers": "Customer Management",
            "suppliers": "Supplier Management",
            "inventory": "Inventory Management",
            "purchases": "Purchase Management",
            "sales": "Sales Management",
            "invoices": "Invoice Management",
            "accounting": "Accounting",
            "reports": "Reports",
            "employees": "Employee Management",
            "settings": "Settings"
        }

        title = section_titles.get(section, section.title())
        self.page_title.configure(text=_(f"{section}.title", title))
        self.breadcrumb.configure(text=f"Home > {title}")

        # Clear current content
        for widget in self.content_area.winfo_children():
            widget.destroy()

        # Load section content
        if section == "dashboard":
            self.load_dashboard_content()
        else:
            self.load_placeholder_content(section, title)

    def set_active_nav(self, active_key):
        """Set active navigation item"""
        for key, button in self.nav_buttons.items():
            if key == active_key:
                button.configure(
                    fg_color=self.colors['primary'],
                    text_color=self.colors['white']
                )
            else:
                button.configure(
                    fg_color="transparent",
                    text_color=self.colors['light']
                )

    def load_placeholder_content(self, section, title):
        """Load placeholder content for sections under development"""
        # Coming soon frame
        coming_soon_frame = ctk.CTkFrame(
            self.content_area,
            fg_color=self.colors['light'],
            corner_radius=12
        )
        coming_soon_frame.pack(fill="both", expand=True, pady=20)

        # Icon and title
        icon_label = ctk.CTkLabel(
            coming_soon_frame,
            text="🚀",
            font=ctk.CTkFont(size=48)
        )
        icon_label.pack(pady=(50, 20))

        title_label = ctk.CTkLabel(
            coming_soon_frame,
            text=f"{title} Module",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=self.colors['dark']
        )
        title_label.pack(pady=(0, 10))

        subtitle_label = ctk.CTkLabel(
            coming_soon_frame,
            text="This module is ready for implementation in the next development phase.",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        subtitle_label.pack(pady=(0, 20))

        # Features list
        features_frame = ctk.CTkFrame(
            coming_soon_frame,
            fg_color=self.colors['white'],
            corner_radius=8
        )
        features_frame.pack(pady=20, padx=50, fill="x")

        features_title = ctk.CTkLabel(
            features_frame,
            text="Planned Features:",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['dark']
        )
        features_title.pack(pady=(20, 10))

        # Sample features based on section
        section_features = {
            "customers": ["Customer database", "Contact management", "Credit tracking", "Purchase history"],
            "suppliers": ["Supplier database", "Contact management", "Payment terms", "Purchase tracking"],
            "inventory": ["Product catalog", "Stock management", "Low stock alerts", "Barcode support"],
            "sales": ["Sales orders", "Customer invoicing", "Payment tracking", "Sales reports"],
            "purchases": ["Purchase orders", "Supplier management", "Receiving", "Cost tracking"],
            "invoices": ["Invoice generation", "PDF creation", "Email sending", "Payment tracking"],
            "accounting": ["Chart of accounts", "Journal entries", "Financial reports", "Tax management"],
            "reports": ["Financial reports", "Sales analytics", "Inventory reports", "Custom reports"],
            "employees": ["Employee records", "Role management", "Attendance", "Payroll integration"],
            "settings": ["System configuration", "User management", "Backup settings", "Customization"]
        }

        features = section_features.get(section, ["Advanced functionality", "Professional interface", "Data management", "Reporting capabilities"])

        for feature in features:
            feature_label = ctk.CTkLabel(
                features_frame,
                text=f"✓ {feature}",
                font=ctk.CTkFont(size=12),
                text_color=self.colors['success'],
                anchor="w"
            )
            feature_label.pack(pady=2, padx=20, anchor="w")

        # Spacer
        ctk.CTkLabel(features_frame, text="").pack(pady=10)

    def configure_rtl_layout(self):
        """Configure layout for right-to-left languages"""
        # This would involve adjusting text alignment and layout direction
        # For now, we'll keep the basic layout but this can be enhanced
        pass
    
    def on_language_change(self, selected_language):
        """Handle language change"""
        localization_manager.set_language(selected_language)
        self.refresh_ui()
    
    def refresh_ui(self):
        """Refresh UI with new language without destroying the window"""
        if not self.window:
            return

        # Update window title
        self.window.title(_("app.title", "Business Management System"))

        # Update all text elements recursively
        self.update_widget_texts(self.window)

        # Update specific elements that we have references to
        if self.welcome_label:
            self.welcome_label.configure(
                text=f"{self.user_info['first_name']} {self.user_info['last_name']}"
            )

        if self.logout_button:
            self.logout_button.configure(text=_("navigation.logout", "Logout"))

        # Update navigation buttons
        if hasattr(self, 'nav_buttons'):
            nav_items = [
                ("dashboard", "Dashboard", "📊"),
                ("customers", "Customers", "👥"),
                ("suppliers", "Suppliers", "🏢"),
                ("inventory", "Inventory", "📦"),
                ("purchases", "Purchases", "🛒"),
                ("sales", "Sales", "💰"),
                ("invoices", "Invoices", "📄"),
                ("accounting", "Accounting", "📈"),
                ("reports", "Reports", "📋"),
                ("employees", "Employees", "👤"),
                ("settings", "Settings", "⚙️")
            ]

            for key, label, icon in nav_items:
                if key in self.nav_buttons:
                    self.nav_buttons[key].configure(
                        text=f"{icon}  {_(f'navigation.{key}', label)}"
                    )

        # Update page title if it exists
        if hasattr(self, 'page_title'):
            current_text = self.page_title.cget("text")
            # Try to determine current section and update accordingly
            for section in ["dashboard", "customers", "suppliers", "inventory", "purchases", "sales", "invoices", "accounting", "reports", "employees", "settings"]:
                if _(f"{section}.title", section.title()) in current_text:
                    self.page_title.configure(text=_(f"{section}.title", section.title()))
                    break

        # Configure RTL layout if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()
    
    def update_widget_texts(self, widget):
        """Recursively update text in all widgets"""
        try:
            widget_class = widget.__class__.__name__
            
            if widget_class == "CTkLabel":
                current_text = widget.cget("text")
                if "Business Management System" in current_text or "نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("app.title", "Business Management System"))
                elif "Language" in current_text or "اللغة" in current_text:
                    widget.configure(text=_("settings.language", "Language"))
                elif "Welcome to Business Management System" in current_text or "مرحباً بك في نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("dashboard.welcome_message", "Welcome to Business Management System"))
            
            elif widget_class == "CTkButton":
                current_text = widget.cget("text")
                if "Logout" in current_text or "تسجيل الخروج" in current_text:
                    widget.configure(text=_("navigation.logout", "Logout"))
            
            # Recursively update child widgets
            try:
                for child in widget.winfo_children():
                    self.update_widget_texts(child)
            except:
                pass
                
        except Exception as e:
            pass
    
    def on_logout_click(self):
        """Handle logout button click"""
        if messagebox.askyesno(
            _("navigation.logout", "Logout"),
            "Are you sure you want to logout?"
        ):
            # Call logout callback which will handle window cleanup
            if self.logout_callback:
                self.logout_callback()
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno(
            "Exit Application",
            "Are you sure you want to exit?"
        ):
            # This is a full application exit, not just logout
            # We need to properly close the application
            if self.logout_callback:
                # Use the logout callback to handle proper application closure
                import sys
                sys.exit(0)
    
    def show(self):
        """Show the main window"""
        if self.window:
            self.window.deiconify()
            self.window.lift()
            self.window.focus_force()
    
    def destroy(self):
        """Destroy the main window"""
        if self.window:
            self.window.destroy()
