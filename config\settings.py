"""
Application Settings and Configuration
"""
import os
import json
from pathlib import Path

class Settings:
    def __init__(self):
        self.app_name = "Business Management System"
        self.version = "1.0.0"
        self.base_dir = Path(__file__).parent.parent
        self.data_dir = self.base_dir / "data"
        self.assets_dir = self.base_dir / "assets"
        self.localization_dir = self.base_dir / "localization"
        
        # Database settings
        self.database_path = self.data_dir / "database.db"
        self.backup_dir = self.data_dir / "backups"
        self.uploads_dir = self.data_dir / "uploads"
        
        # UI settings
        self.default_language = "en"
        self.supported_languages = ["en", "ar"]
        self.default_theme = "light"
        self.window_size = (1200, 800)
        self.min_window_size = (800, 600)
        
        # Security settings
        self.session_timeout = 3600  # 1 hour in seconds
        self.max_login_attempts = 3
        self.password_min_length = 8
        
        # Business settings
        self.default_currency = "USD"
        self.tax_rate = 0.15
        self.company_name = ""
        self.company_logo = ""
        
        # Create directories if they don't exist
        self._create_directories()
        
        # Load user settings
        self.load_user_settings()
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = [
            self.data_dir,
            self.backup_dir,
            self.uploads_dir,
            self.assets_dir / "icons",
            self.assets_dir / "images",
            self.assets_dir / "themes"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def load_user_settings(self):
        """Load user-specific settings from file"""
        settings_file = self.data_dir / "user_settings.json"
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    user_settings = json.load(f)
                    
                # Update settings with user preferences
                for key, value in user_settings.items():
                    if hasattr(self, key):
                        setattr(self, key, value)
            except Exception as e:
                print(f"Error loading user settings: {e}")
    
    def save_user_settings(self):
        """Save user-specific settings to file"""
        settings_file = self.data_dir / "user_settings.json"
        user_settings = {
            "default_language": self.default_language,
            "default_theme": self.default_theme,
            "window_size": self.window_size,
            "default_currency": self.default_currency,
            "tax_rate": self.tax_rate,
            "company_name": self.company_name,
            "company_logo": self.company_logo
        }
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(user_settings, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving user settings: {e}")
    
    def get_localization_file(self, language):
        """Get path to localization file for given language"""
        return self.localization_dir / f"{language}.json"

# Global settings instance
settings = Settings()
