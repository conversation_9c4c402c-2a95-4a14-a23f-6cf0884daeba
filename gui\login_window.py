"""
Login Window - User authentication interface
"""
import customtkinter as ctk
from tkinter import messagebox
import threading
from core.auth import auth_manager
from core.localization import localization_manager, _
from config.settings import settings

class LoginWindow:
    """Login window for user authentication"""
    
    def __init__(self):
        self.window = None
        self.username_entry = None
        self.password_entry = None
        self.remember_var = None
        self.language_var = None
        self.login_callback = None
        
        # Set appearance mode and color theme
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
    
    def create_window(self, login_callback=None):
        """Create and display the login window"""
        self.login_callback = login_callback
        
        # Create main window
        self.window = ctk.CTk()
        self.window.title(_("login.title", "Login"))
        self.window.geometry("400x500")
        self.window.resizable(False, False)
        
        # Center the window
        self.center_window()
        
        # Create UI elements
        self.create_ui()
        
        # Configure for RTL if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()
        
        return self.window
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_ui(self):
        """Create the user interface elements"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Logo/Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=_("app.title", "Business Management System"),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(30, 10))
        
        version_label = ctk.CTkLabel(
            main_frame,
            text=_("app.version", "Version 1.0.0"),
            font=ctk.CTkFont(size=12)
        )
        version_label.pack(pady=(0, 30))
        
        # Language selection
        language_frame = ctk.CTkFrame(main_frame)
        language_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        language_label = ctk.CTkLabel(
            language_frame,
            text=_("settings.language", "Language"),
            font=ctk.CTkFont(size=14)
        )
        language_label.pack(pady=(10, 5))
        
        self.language_var = ctk.StringVar(value=localization_manager.current_language)
        language_menu = ctk.CTkOptionMenu(
            language_frame,
            variable=self.language_var,
            values=list(localization_manager.get_available_languages().keys()),
            command=self.on_language_change
        )
        language_menu.pack(pady=(0, 10))
        
        # Login form
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.pack(fill="x", padx=20, pady=20)
        
        # Username
        username_label = ctk.CTkLabel(
            form_frame,
            text=_("login.username", "Username"),
            font=ctk.CTkFont(size=14)
        )
        username_label.pack(pady=(20, 5))
        
        self.username_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text=_("login.username", "Username"),
            font=ctk.CTkFont(size=12),
            height=35
        )
        self.username_entry.pack(fill="x", padx=20, pady=(0, 10))
        
        # Password
        password_label = ctk.CTkLabel(
            form_frame,
            text=_("login.password", "Password"),
            font=ctk.CTkFont(size=14)
        )
        password_label.pack(pady=(10, 5))
        
        self.password_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text=_("login.password", "Password"),
            show="*",
            font=ctk.CTkFont(size=12),
            height=35
        )
        self.password_entry.pack(fill="x", padx=20, pady=(0, 10))
        
        # Remember me checkbox
        self.remember_var = ctk.BooleanVar()
        remember_checkbox = ctk.CTkCheckBox(
            form_frame,
            text=_("login.remember_me", "Remember me"),
            variable=self.remember_var,
            font=ctk.CTkFont(size=12)
        )
        remember_checkbox.pack(pady=10)
        
        # Login button
        login_button = ctk.CTkButton(
            form_frame,
            text=_("login.login_button", "Login"),
            command=self.on_login_click,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        login_button.pack(fill="x", padx=20, pady=(20, 10))
        
        # Forgot password link
        forgot_label = ctk.CTkLabel(
            form_frame,
            text=_("login.forgot_password", "Forgot password?"),
            font=ctk.CTkFont(size=12),
            text_color="blue",
            cursor="hand2"
        )
        forgot_label.pack(pady=(0, 20))
        forgot_label.bind("<Button-1>", self.on_forgot_password)
        
        # Bind Enter key to login
        self.window.bind('<Return>', lambda event: self.on_login_click())
        
        # Focus on username entry
        self.username_entry.focus()
    
    def configure_rtl_layout(self):
        """Configure layout for right-to-left languages"""
        # This would involve adjusting text alignment and layout direction
        # For now, we'll keep the basic layout but this can be enhanced
        pass
    
    def on_language_change(self, selected_language):
        """Handle language change"""
        localization_manager.set_language(selected_language)
        self.refresh_ui()

    def refresh_ui(self):
        """Refresh UI with new language without destroying the window"""
        if not self.window:
            return

        # Update window title
        self.window.title(_("login.title", "Login"))

        # Find and update all text elements
        self.update_widget_texts(self.window)

        # Configure RTL layout if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()

    def update_widget_texts(self, widget):
        """Recursively update text in all widgets"""
        try:
            # Update different widget types
            widget_class = widget.__class__.__name__

            if widget_class == "CTkLabel":
                # Update labels based on their current text
                current_text = widget.cget("text")
                if current_text == "Business Management System" or "نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("app.title", "Business Management System"))
                elif current_text == "Version 1.0.0" or "الإصدار" in current_text:
                    widget.configure(text=_("app.version", "Version 1.0.0"))
                elif current_text == "Language" or "اللغة" in current_text:
                    widget.configure(text=_("settings.language", "Language"))
                elif current_text == "Username" or "اسم المستخدم" in current_text:
                    widget.configure(text=_("login.username", "Username"))
                elif current_text == "Password" or "كلمة المرور" in current_text:
                    widget.configure(text=_("login.password", "Password"))
                elif current_text == "Forgot password?" or "نسيت كلمة المرور؟" in current_text:
                    widget.configure(text=_("login.forgot_password", "Forgot password?"))

            elif widget_class == "CTkButton":
                current_text = widget.cget("text")
                if current_text == "Login" or "دخول" in current_text:
                    widget.configure(text=_("login.login_button", "Login"))

            elif widget_class == "CTkCheckBox":
                current_text = widget.cget("text")
                if current_text == "Remember me" or "تذكرني" in current_text:
                    widget.configure(text=_("login.remember_me", "Remember me"))

            elif widget_class == "CTkEntry":
                # Update placeholder text
                if hasattr(widget, '_placeholder_text'):
                    current_placeholder = widget._placeholder_text
                    if current_placeholder == "Username" or "اسم المستخدم" in current_placeholder:
                        widget.configure(placeholder_text=_("login.username", "Username"))
                    elif current_placeholder == "Password" or "كلمة المرور" in current_placeholder:
                        widget.configure(placeholder_text=_("login.password", "Password"))

            # Recursively update child widgets
            try:
                for child in widget.winfo_children():
                    self.update_widget_texts(child)
            except:
                pass  # Some widgets may not have children

        except Exception as e:
            # Continue updating other widgets even if one fails
            pass
    
    def on_login_click(self):
        """Handle login button click"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror(
                _("common.error", "Error"),
                _("validation.required_field", "Please fill in all fields")
            )
            return
        
        # Disable login button during authentication
        login_button = None
        for widget in self.window.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                for child in widget.winfo_children():
                    if isinstance(child, ctk.CTkFrame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ctk.CTkButton):
                                login_button = grandchild
                                break
        
        if login_button:
            login_button.configure(state="disabled", text=_("common.loading", "Loading..."))
        
        # Perform authentication in separate thread
        threading.Thread(
            target=self.authenticate_user,
            args=(username, password),
            daemon=True
        ).start()
    
    def authenticate_user(self, username, password):
        """Authenticate user in background thread"""
        try:
            result = auth_manager.login(username, password)
            
            # Update UI in main thread
            self.window.after(0, self.handle_login_result, result)
            
        except Exception as e:
            error_result = {
                'success': False,
                'message': f"Authentication error: {str(e)}"
            }
            self.window.after(0, self.handle_login_result, error_result)
    
    def handle_login_result(self, result):
        """Handle login result in main thread"""
        # Re-enable login button
        login_button = None
        for widget in self.window.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                for child in widget.winfo_children():
                    if isinstance(child, ctk.CTkFrame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ctk.CTkButton):
                                login_button = grandchild
                                break
        
        if login_button:
            login_button.configure(state="normal", text=_("login.login_button", "Login"))
        
        if result['success']:
            messagebox.showinfo(
                _("common.success", "Success"),
                _("login.login_successful", "Login successful")
            )
            
            # Call login callback if provided
            if self.login_callback:
                self.login_callback(result)
            
            # Close login window
            self.window.destroy()
        else:
            messagebox.showerror(
                _("common.error", "Error"),
                result['message']
            )
            
            # Clear password field
            self.password_entry.delete(0, 'end')
            self.password_entry.focus()
    
    def on_forgot_password(self, event):
        """Handle forgot password click"""
        messagebox.showinfo(
            _("common.info", "Information"),
            "Please contact your system administrator to reset your password."
        )
    
    def show(self):
        """Show the login window"""
        if self.window:
            self.window.mainloop()
    
    def destroy(self):
        """Destroy the login window"""
        if self.window:
            self.window.destroy()
