"""
Login Window - User authentication interface
"""
import customtkinter as ctk
from tkinter import messagebox
from core.auth import auth_manager
from core.localization import localization_manager, _
from config.settings import settings

class LoginWindow:
    """Login window for user authentication"""
    
    def __init__(self):
        self.window = None
        self.username_entry = None
        self.password_entry = None
        self.remember_var = None
        self.language_var = None
        self.login_callback = None
        
        # Set professional appearance mode and color theme
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")

        # Professional color scheme
        self.colors = {
            'primary': '#1f538d',
            'secondary': '#2c5aa0',
            'accent': '#e8f4fd',
            'success': '#28a745',
            'warning': '#ffc107',
            'danger': '#dc3545',
            'dark': '#343a40',
            'light': '#f8f9fa',
            'white': '#ffffff',
            'border': '#dee2e6',
            'text_primary': '#212529',
            'text_secondary': '#6c757d'
        }
    
    def create_window(self, login_callback=None):
        """Create and display the login window"""
        self.login_callback = login_callback
        
        # Create main window with professional styling
        self.window = ctk.CTk()
        self.window.title(_("login.title", "Login"))
        self.window.geometry("450x600")
        self.window.resizable(False, False)
        self.window.configure(fg_color=self.colors['light'])
        
        # Center the window
        self.center_window()
        
        # Create UI elements
        self.create_ui()
        
        # Configure for RTL if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()
        
        return self.window
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_ui(self):
        """Create the professional user interface elements"""
        # Header section with branding
        header_frame = ctk.CTkFrame(
            self.window,
            height=120,
            fg_color=self.colors['primary'],
            corner_radius=0
        )
        header_frame.pack(fill="x", side="top")
        header_frame.pack_propagate(False)

        # Logo placeholder
        logo_frame = ctk.CTkFrame(
            header_frame,
            width=60,
            height=60,
            fg_color=self.colors['white'],
            corner_radius=30
        )
        logo_frame.pack(pady=(20, 10))
        logo_frame.pack_propagate(False)

        logo_label = ctk.CTkLabel(
            logo_frame,
            text="BMS",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=self.colors['primary']
        )
        logo_label.pack(expand=True)

        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text=_("app.title", "Business Management System"),
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=self.colors['white']
        )
        title_label.pack(pady=(0, 10))

        # Main content frame
        main_frame = ctk.CTkFrame(
            self.window,
            fg_color=self.colors['white'],
            corner_radius=0
        )
        main_frame.pack(fill="both", expand=True)
        
        # Welcome section
        welcome_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        welcome_frame.pack(fill="x", padx=30, pady=(30, 20))

        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text=_("login.title", "Sign In"),
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=self.colors['dark']
        )
        welcome_title.pack(pady=(0, 5))

        welcome_subtitle = ctk.CTkLabel(
            welcome_frame,
            text="Access your business management dashboard",
            font=ctk.CTkFont(size=14),
            text_color=self.colors['text_secondary']
        )
        welcome_subtitle.pack(pady=(0, 20))

        # Language selection (compact)
        language_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        language_frame.pack(fill="x", padx=30, pady=(0, 20))

        lang_container = ctk.CTkFrame(language_frame, fg_color=self.colors['light'], corner_radius=8)
        lang_container.pack(fill="x", pady=5)

        language_label = ctk.CTkLabel(
            lang_container,
            text=_("settings.language", "Language"),
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=self.colors['text_primary']
        )
        language_label.pack(side="left", padx=(15, 10), pady=10)

        self.language_var = ctk.StringVar(value=localization_manager.current_language)
        language_menu = ctk.CTkOptionMenu(
            lang_container,
            variable=self.language_var,
            values=list(localization_manager.get_available_languages().keys()),
            command=self.on_language_change,
            width=100,
            height=30,
            font=ctk.CTkFont(size=11),
            fg_color=self.colors['white'],
            button_color=self.colors['primary'],
            text_color=self.colors['dark']
        )
        language_menu.pack(side="right", padx=15, pady=5)
        
        # Login form
        form_frame = ctk.CTkFrame(
            main_frame,
            fg_color=self.colors['light'],
            corner_radius=12
        )
        form_frame.pack(fill="x", padx=30, pady=(0, 20))

        # Form title
        form_title = ctk.CTkLabel(
            form_frame,
            text="Enter your credentials",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=self.colors['dark']
        )
        form_title.pack(pady=(25, 20))

        # Username field
        username_container = ctk.CTkFrame(form_frame, fg_color="transparent")
        username_container.pack(fill="x", padx=25, pady=(0, 15))

        username_label = ctk.CTkLabel(
            username_container,
            text=_("login.username", "Username"),
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=self.colors['text_primary'],
            anchor="w"
        )
        username_label.pack(fill="x", pady=(0, 5))

        self.username_entry = ctk.CTkEntry(
            username_container,
            placeholder_text="Enter your username",
            font=ctk.CTkFont(size=13),
            height=45,
            fg_color=self.colors['white'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary']
        )
        self.username_entry.pack(fill="x")

        # Password field
        password_container = ctk.CTkFrame(form_frame, fg_color="transparent")
        password_container.pack(fill="x", padx=25, pady=(0, 15))

        password_label = ctk.CTkLabel(
            password_container,
            text=_("login.password", "Password"),
            font=ctk.CTkFont(size=13, weight="bold"),
            text_color=self.colors['text_primary'],
            anchor="w"
        )
        password_label.pack(fill="x", pady=(0, 5))

        self.password_entry = ctk.CTkEntry(
            password_container,
            placeholder_text="Enter your password",
            show="*",
            font=ctk.CTkFont(size=13),
            height=45,
            fg_color=self.colors['white'],
            border_color=self.colors['border'],
            text_color=self.colors['text_primary']
        )
        self.password_entry.pack(fill="x")

        # Remember me and forgot password
        options_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        options_frame.pack(fill="x", padx=25, pady=(0, 20))

        self.remember_var = ctk.BooleanVar()
        remember_checkbox = ctk.CTkCheckBox(
            options_frame,
            text=_("login.remember_me", "Remember me"),
            variable=self.remember_var,
            font=ctk.CTkFont(size=12),
            text_color=self.colors['text_secondary']
        )
        remember_checkbox.pack(side="left")

        forgot_label = ctk.CTkLabel(
            options_frame,
            text=_("login.forgot_password", "Forgot password?"),
            font=ctk.CTkFont(size=12),
            text_color=self.colors['primary'],
            cursor="hand2"
        )
        forgot_label.pack(side="right")
        forgot_label.bind("<Button-1>", self.on_forgot_password)

        # Login button
        login_button = ctk.CTkButton(
            form_frame,
            text=_("login.login_button", "Sign In"),
            command=self.on_login_click,
            font=ctk.CTkFont(size=15, weight="bold"),
            height=50,
            fg_color=self.colors['primary'],
            hover_color=self.colors['secondary']
        )
        login_button.pack(fill="x", padx=25, pady=(0, 25))

        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        footer_frame.pack(fill="x", padx=30, pady=(0, 20))

        version_label = ctk.CTkLabel(
            footer_frame,
            text=_("app.version", "Version 1.0.0"),
            font=ctk.CTkFont(size=11),
            text_color=self.colors['text_secondary']
        )
        version_label.pack()
        
        # Bind Enter key to login
        self.window.bind('<Return>', lambda event: self.on_login_click())
        
        # Focus on username entry
        self.username_entry.focus()
    
    def configure_rtl_layout(self):
        """Configure layout for right-to-left languages"""
        # This would involve adjusting text alignment and layout direction
        # For now, we'll keep the basic layout but this can be enhanced
        pass
    
    def on_language_change(self, selected_language):
        """Handle language change"""
        localization_manager.set_language(selected_language)
        self.refresh_ui()

    def refresh_ui(self):
        """Refresh UI with new language without destroying the window"""
        if not self.window:
            return

        # Update window title
        self.window.title(_("login.title", "Login"))

        # Find and update all text elements
        self.update_widget_texts(self.window)

        # Configure RTL layout if needed
        if localization_manager.is_rtl():
            self.configure_rtl_layout()

    def update_widget_texts(self, widget):
        """Recursively update text in all widgets"""
        try:
            # Update different widget types
            widget_class = widget.__class__.__name__

            if widget_class == "CTkLabel":
                # Update labels based on their current text
                current_text = widget.cget("text")
                if current_text == "Business Management System" or "نظام إدارة الأعمال" in current_text:
                    widget.configure(text=_("app.title", "Business Management System"))
                elif current_text == "Version 1.0.0" or "الإصدار" in current_text:
                    widget.configure(text=_("app.version", "Version 1.0.0"))
                elif current_text == "Language" or "اللغة" in current_text:
                    widget.configure(text=_("settings.language", "Language"))
                elif current_text == "Username" or "اسم المستخدم" in current_text:
                    widget.configure(text=_("login.username", "Username"))
                elif current_text == "Password" or "كلمة المرور" in current_text:
                    widget.configure(text=_("login.password", "Password"))
                elif current_text == "Forgot password?" or "نسيت كلمة المرور؟" in current_text:
                    widget.configure(text=_("login.forgot_password", "Forgot password?"))

            elif widget_class == "CTkButton":
                current_text = widget.cget("text")
                if current_text == "Login" or "دخول" in current_text:
                    widget.configure(text=_("login.login_button", "Login"))

            elif widget_class == "CTkCheckBox":
                current_text = widget.cget("text")
                if current_text == "Remember me" or "تذكرني" in current_text:
                    widget.configure(text=_("login.remember_me", "Remember me"))

            elif widget_class == "CTkEntry":
                # Update placeholder text
                if hasattr(widget, '_placeholder_text'):
                    current_placeholder = widget._placeholder_text
                    if current_placeholder == "Username" or "اسم المستخدم" in current_placeholder:
                        widget.configure(placeholder_text=_("login.username", "Username"))
                    elif current_placeholder == "Password" or "كلمة المرور" in current_placeholder:
                        widget.configure(placeholder_text=_("login.password", "Password"))

            # Recursively update child widgets
            try:
                for child in widget.winfo_children():
                    self.update_widget_texts(child)
            except:
                pass  # Some widgets may not have children

        except Exception as e:
            # Continue updating other widgets even if one fails
            pass
    
    def on_login_click(self):
        """Handle login button click"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror(
                _("common.error", "Error"),
                _("validation.required_field", "Please fill in all fields")
            )
            return
        
        # Disable login button during authentication
        login_button = None
        for widget in self.window.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                for child in widget.winfo_children():
                    if isinstance(child, ctk.CTkFrame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ctk.CTkButton):
                                login_button = grandchild
                                break

        if login_button:
            login_button.configure(state="disabled", text=_("common.loading", "Loading..."))

        # Update UI to show loading state
        self.window.update()

        # Perform authentication directly (no threading needed for simple login)
        try:
            result = auth_manager.login(username, password)
            self.handle_login_result(result)
        except Exception as e:
            error_result = {
                'success': False,
                'message': f"Authentication error: {str(e)}"
            }
            self.handle_login_result(error_result)
    

    
    def handle_login_result(self, result):
        """Handle login result in main thread"""
        # Re-enable login button
        login_button = None
        for widget in self.window.winfo_children():
            if isinstance(widget, ctk.CTkFrame):
                for child in widget.winfo_children():
                    if isinstance(child, ctk.CTkFrame):
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ctk.CTkButton):
                                login_button = grandchild
                                break
        
        if login_button:
            login_button.configure(state="normal", text=_("login.login_button", "Login"))
        
        if result['success']:
            messagebox.showinfo(
                _("common.success", "Success"),
                _("login.login_successful", "Login successful")
            )
            
            # Call login callback if provided
            if self.login_callback:
                self.login_callback(result)
            
            # Close login window
            self.window.destroy()
        else:
            messagebox.showerror(
                _("common.error", "Error"),
                result['message']
            )
            
            # Clear password field
            self.password_entry.delete(0, 'end')
            self.password_entry.focus()
    
    def on_forgot_password(self, event):
        """Handle forgot password click"""
        messagebox.showinfo(
            _("common.info", "Information"),
            "Please contact your system administrator to reset your password."
        )
    
    def show(self):
        """Show the login window"""
        if self.window:
            self.window.deiconify()
            self.window.lift()
            self.window.focus_force()
    
    def destroy(self):
        """Destroy the login window"""
        if self.window:
            self.window.destroy()
