"""
Localization Manager - Handles multi-language support
"""
import json
from typing import Dict, Any
from config.settings import settings
import arabic_reshaper
from bidi.algorithm import get_display

class LocalizationManager:
    """Manages application localization and text direction"""
    
    def __init__(self):
        self.current_language = settings.default_language
        self.translations = {}
        self.rtl_languages = ['ar', 'he', 'fa', 'ur']
        self.load_translations()
    
    def load_translations(self):
        """Load all translation files"""
        for language in settings.supported_languages:
            try:
                translation_file = settings.get_localization_file(language)
                if translation_file.exists():
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[language] = json.load(f)
                else:
                    print(f"Translation file not found: {translation_file}")
                    self.translations[language] = {}
            except Exception as e:
                print(f"Error loading translations for {language}: {e}")
                self.translations[language] = {}
    
    def set_language(self, language: str):
        """Set the current language"""
        if language in settings.supported_languages:
            self.current_language = language
            settings.default_language = language
            settings.save_user_settings()
        else:
            print(f"Unsupported language: {language}")
    
    def get_text(self, key: str, default: str = None) -> str:
        """Get translated text for a key"""
        if self.current_language not in self.translations:
            return default or key
        
        # Navigate through nested keys (e.g., "login.username")
        keys = key.split('.')
        value = self.translations[self.current_language]
        
        try:
            for k in keys:
                value = value[k]
            
            # Handle Arabic text reshaping if needed
            if self.current_language == 'ar' and isinstance(value, str):
                return self.reshape_arabic_text(value)
            
            return value
        except (KeyError, TypeError):
            return default or key
    
    def is_rtl(self) -> bool:
        """Check if current language is right-to-left"""
        return self.current_language in self.rtl_languages
    
    def get_text_direction(self) -> str:
        """Get text direction for current language"""
        return 'rtl' if self.is_rtl() else 'ltr'
    
    def reshape_arabic_text(self, text: str) -> str:
        """Reshape Arabic text for proper display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except Exception as e:
            print(f"Error reshaping Arabic text: {e}")
            return text
    
    def format_number(self, number: float, decimal_places: int = 2) -> str:
        """Format number according to current locale"""
        if self.current_language == 'ar':
            # Arabic numerals
            arabic_digits = '٠١٢٣٤٥٦٧٨٩'
            english_digits = '0123456789'
            
            formatted = f"{number:.{decimal_places}f}"
            for eng, ar in zip(english_digits, arabic_digits):
                formatted = formatted.replace(eng, ar)
            return formatted
        else:
            return f"{number:,.{decimal_places}f}"
    
    def format_currency(self, amount: float, currency: str = None) -> str:
        """Format currency according to current locale"""
        if currency is None:
            currency = settings.default_currency
        
        formatted_amount = self.format_number(amount, 2)
        
        if self.current_language == 'ar':
            return f"{formatted_amount} {currency}"
        else:
            return f"{currency} {formatted_amount}"
    
    def format_date(self, date_obj, format_type: str = 'short') -> str:
        """Format date according to current locale"""
        if self.current_language == 'ar':
            # Arabic date format
            if format_type == 'short':
                return date_obj.strftime('%d/%m/%Y')
            else:
                return date_obj.strftime('%d %B %Y')
        else:
            # English date format
            if format_type == 'short':
                return date_obj.strftime('%m/%d/%Y')
            else:
                return date_obj.strftime('%B %d, %Y')
    
    def get_available_languages(self) -> Dict[str, str]:
        """Get list of available languages with their names"""
        return {
            'en': 'English',
            'ar': 'العربية'
        }
    
    def validate_translation_key(self, key: str) -> bool:
        """Check if a translation key exists"""
        keys = key.split('.')
        value = self.translations.get(self.current_language, {})
        
        try:
            for k in keys:
                value = value[k]
            return True
        except (KeyError, TypeError):
            return False
    
    def get_all_translations(self, section: str = None) -> Dict[str, Any]:
        """Get all translations for current language or specific section"""
        translations = self.translations.get(self.current_language, {})
        
        if section:
            return translations.get(section, {})
        
        return translations
    
    def add_translation(self, key: str, value: str, language: str = None):
        """Add or update a translation"""
        if language is None:
            language = self.current_language
        
        if language not in self.translations:
            self.translations[language] = {}
        
        keys = key.split('.')
        current = self.translations[language]
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # Set the value
        current[keys[-1]] = value
    
    def save_translations(self, language: str = None):
        """Save translations to file"""
        if language is None:
            language = self.current_language
        
        if language not in self.translations:
            return
        
        try:
            translation_file = settings.get_localization_file(language)
            with open(translation_file, 'w', encoding='utf-8') as f:
                json.dump(
                    self.translations[language], 
                    f, 
                    indent=2, 
                    ensure_ascii=False
                )
        except Exception as e:
            print(f"Error saving translations for {language}: {e}")

# Global localization manager instance
localization_manager = LocalizationManager()

# Convenience function for getting translated text
def _(key: str, default: str = None) -> str:
    """Shorthand function for getting translated text"""
    return localization_manager.get_text(key, default)
