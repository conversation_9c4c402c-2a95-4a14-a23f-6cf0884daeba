"""
Database Schema and Configuration
"""
import sqlite3
from pathlib import Path
from config.settings import settings

class DatabaseSchema:
    """Database schema definitions"""
    
    @staticmethod
    def get_create_tables_sql():
        """Return SQL statements to create all tables"""
        return [
            # Users and Authentication
            """
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                role_id INTEGER NOT NULL,
                company_id INTEGER NOT NULL,
                branch_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIG<PERSON> KEY (role_id) REFERENCES roles(id),
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (branch_id) REFERENCES branches(id)
            )
            """,
            
            # Roles and Permissions
            """
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(50) UNIQUE NOT NULL,
                description TEXT,
                permissions TEXT, -- JSON string of permissions
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # Companies
            """
            CREATE TABLE IF NOT EXISTS companies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                legal_name VARCHAR(100),
                tax_id VARCHAR(50),
                address TEXT,
                phone VARCHAR(20),
                email VARCHAR(100),
                website VARCHAR(100),
                logo_path VARCHAR(255),
                currency VARCHAR(3) DEFAULT 'USD',
                tax_rate DECIMAL(5,4) DEFAULT 0.15,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # Branches
            """
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                address TEXT,
                phone VARCHAR(20),
                email VARCHAR(100),
                manager_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (manager_id) REFERENCES users(id)
            )
            """,
            
            # Customers
            """
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                customer_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                contact_person VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                tax_id VARCHAR(50),
                credit_limit DECIMAL(15,2) DEFAULT 0,
                payment_terms INTEGER DEFAULT 30, -- days
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
            """,
            
            # Suppliers
            """
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                supplier_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                contact_person VARCHAR(100),
                email VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50),
                tax_id VARCHAR(50),
                payment_terms INTEGER DEFAULT 30, -- days
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
            """,
            
            # Product Categories
            """
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (parent_id) REFERENCES categories(id)
            )
            """,
            
            # Products/Inventory
            """
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                product_code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                category_id INTEGER,
                unit_of_measure VARCHAR(20) DEFAULT 'pcs',
                cost_price DECIMAL(15,4) DEFAULT 0,
                selling_price DECIMAL(15,4) DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                max_stock_level INTEGER DEFAULT 0,
                current_stock INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                image_path VARCHAR(255),
                barcode VARCHAR(100),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            )
            """,

            # Chart of Accounts
            """
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                account_code VARCHAR(20) UNIQUE NOT NULL,
                account_name VARCHAR(100) NOT NULL,
                account_type VARCHAR(20) NOT NULL, -- Asset, Liability, Equity, Revenue, Expense
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (parent_id) REFERENCES accounts(id)
            )
            """,

            # Transactions
            """
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                branch_id INTEGER,
                transaction_type VARCHAR(20) NOT NULL, -- Sale, Purchase, Payment, Receipt
                reference_number VARCHAR(50) UNIQUE NOT NULL,
                transaction_date DATE NOT NULL,
                customer_id INTEGER,
                supplier_id INTEGER,
                total_amount DECIMAL(15,2) NOT NULL,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                net_amount DECIMAL(15,2) NOT NULL,
                status VARCHAR(20) DEFAULT 'Pending', -- Pending, Completed, Cancelled
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (branch_id) REFERENCES branches(id),
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            """,

            # Transaction Items
            """
            CREATE TABLE IF NOT EXISTS transaction_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(15,4) NOT NULL,
                total_price DECIMAL(15,2) NOT NULL,
                discount_percent DECIMAL(5,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                tax_percent DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                net_amount DECIMAL(15,2) NOT NULL,
                FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
            """,

            # Invoices
            """
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                transaction_id INTEGER,
                invoice_type VARCHAR(20) NOT NULL, -- Sales, Purchase
                invoice_date DATE NOT NULL,
                due_date DATE,
                customer_id INTEGER,
                supplier_id INTEGER,
                subtotal DECIMAL(15,2) NOT NULL,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                balance_amount DECIMAL(15,2) NOT NULL,
                status VARCHAR(20) DEFAULT 'Draft', -- Draft, Sent, Paid, Overdue, Cancelled
                pdf_path VARCHAR(255),
                notes TEXT,
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (transaction_id) REFERENCES transactions(id),
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            """,

            # Employees
            """
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                employee_code VARCHAR(20) UNIQUE NOT NULL,
                first_name VARCHAR(50) NOT NULL,
                last_name VARCHAR(50) NOT NULL,
                email VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                address TEXT,
                position VARCHAR(100),
                department VARCHAR(100),
                hire_date DATE,
                salary DECIMAL(15,2),
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id)
            )
            """,

            # Journal Entries
            """
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                entry_number VARCHAR(50) UNIQUE NOT NULL,
                entry_date DATE NOT NULL,
                reference VARCHAR(100),
                description TEXT,
                total_debit DECIMAL(15,2) NOT NULL,
                total_credit DECIMAL(15,2) NOT NULL,
                status VARCHAR(20) DEFAULT 'Draft', -- Draft, Posted
                created_by INTEGER NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            """,

            # Journal Entry Lines
            """
            CREATE TABLE IF NOT EXISTS journal_entry_lines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                debit_amount DECIMAL(15,2) DEFAULT 0,
                credit_amount DECIMAL(15,2) DEFAULT 0,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts(id)
            )
            """,

            # Notifications
            """
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                user_id INTEGER,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                notification_type VARCHAR(50) DEFAULT 'info', -- info, warning, error, success
                is_read BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            """,

            # Audit Log
            """
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                user_id INTEGER,
                table_name VARCHAR(50) NOT NULL,
                record_id INTEGER,
                action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
                old_values TEXT, -- JSON
                new_values TEXT, -- JSON
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            """,

            # System Settings
            """
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                setting_key VARCHAR(100) NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
                description TEXT,
                updated_by INTEGER,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (company_id) REFERENCES companies(id),
                FOREIGN KEY (updated_by) REFERENCES users(id),
                UNIQUE(company_id, setting_key)
            )
            """,

            # Backups
            """
            CREATE TABLE IF NOT EXISTS backups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_name VARCHAR(200) NOT NULL,
                backup_path VARCHAR(500) NOT NULL,
                backup_size INTEGER,
                backup_type VARCHAR(20) DEFAULT 'manual', -- manual, automatic
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
            """,

            # User Sessions
            """
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(255) UNIQUE NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                login_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
            """
        ]

def initialize_database():
    """Initialize the database with schema and default data"""
    db_path = settings.database_path
    
    # Create database directory if it doesn't exist
    db_path.parent.mkdir(parents=True, exist_ok=True)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Create tables
        schema = DatabaseSchema()
        for sql in schema.get_create_tables_sql():
            cursor.execute(sql)
        
        # Insert default data
        _insert_default_data(cursor)
        
        conn.commit()
        print("Database initialized successfully")
        
    except Exception as e:
        conn.rollback()
        print(f"Error initializing database: {e}")
        raise
    finally:
        conn.close()

def _insert_default_data(cursor):
    """Insert default data into the database"""
    # Default company
    cursor.execute("""
        INSERT OR IGNORE INTO companies (id, name, legal_name, currency, tax_rate)
        VALUES (1, 'Default Company', 'Default Company Ltd.', 'USD', 0.15)
    """)
    
    # Default roles
    default_roles = [
        (1, 'Super Admin', 'Full system access', '{"all": true}'),
        (2, 'Admin', 'Administrative access', '{"admin": true}'),
        (3, 'Manager', 'Management access', '{"manager": true}'),
        (4, 'Employee', 'Basic employee access', '{"employee": true}'),
        (5, 'Viewer', 'Read-only access', '{"viewer": true}')
    ]
    
    cursor.executemany("""
        INSERT OR IGNORE INTO roles (id, name, description, permissions)
        VALUES (?, ?, ?, ?)
    """, default_roles)
    
    # Default admin user (password: admin123)
    cursor.execute("""
        INSERT OR IGNORE INTO users
        (id, username, email, password_hash, first_name, last_name, role_id, company_id)
        VALUES (1, 'admin', '<EMAIL>',
                '$2b$12$3NhXMbjdBWpHbdMyUQnjO.WTv/XNqu7ZJQnvZSAeA1GVqC1LO91V6',
                'System', 'Administrator', 1, 1)
    """)

if __name__ == "__main__":
    initialize_database()
