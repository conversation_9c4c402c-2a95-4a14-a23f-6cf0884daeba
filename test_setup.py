"""
Test Setup Script - Verify application installation and configuration
"""
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import customtkinter
        print("✓ customtkinter imported successfully")
    except ImportError as e:
        print(f"✗ customtkinter import failed: {e}")
        return False
    
    try:
        import bcrypt
        print("✓ bcrypt imported successfully")
    except ImportError as e:
        print(f"✗ bcrypt import failed: {e}")
        return False
    
    try:
        import arabic_reshaper
        print("✓ arabic_reshaper imported successfully")
    except ImportError as e:
        print(f"✗ arabic_reshaper import failed: {e}")
        return False
    
    try:
        from bidi.algorithm import get_display
        print("✓ python-bidi imported successfully")
    except ImportError as e:
        print(f"✗ python-bidi import failed: {e}")
        return False
    
    try:
        from config.settings import settings
        print("✓ Application settings loaded successfully")
    except ImportError as e:
        print(f"✗ Settings import failed: {e}")
        return False
    
    try:
        from core.database_manager import db_manager
        print("✓ Database manager imported successfully")
    except ImportError as e:
        print(f"✗ Database manager import failed: {e}")
        return False
    
    try:
        from core.auth import auth_manager
        print("✓ Authentication manager imported successfully")
    except ImportError as e:
        print(f"✗ Authentication manager import failed: {e}")
        return False
    
    try:
        from core.localization import localization_manager
        print("✓ Localization manager imported successfully")
    except ImportError as e:
        print(f"✗ Localization manager import failed: {e}")
        return False
    
    return True

def test_database_setup():
    """Test database initialization"""
    print("\nTesting database setup...")
    
    try:
        from config.database import initialize_database
        initialize_database()
        print("✓ Database initialized successfully")
        
        # Test database connection
        from core.database_manager import db_manager
        
        # Test basic query
        result = db_manager.execute_scalar("SELECT COUNT(*) FROM users")
        print(f"✓ Database connection working - {result} users found")
        
        # Test default admin user
        admin_user = db_manager.execute_query(
            "SELECT username, email FROM users WHERE username = ?",
            ("admin",)
        )
        
        if admin_user:
            print(f"✓ Default admin user found: {admin_user[0]['username']} ({admin_user[0]['email']})")
        else:
            print("✗ Default admin user not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False

def test_authentication():
    """Test authentication system"""
    print("\nTesting authentication...")
    
    try:
        from core.auth import auth_manager
        
        # Test password hashing
        test_password = "test123"
        hashed = auth_manager.hash_password(test_password)
        print("✓ Password hashing working")
        
        # Test password verification
        if auth_manager.verify_password(test_password, hashed):
            print("✓ Password verification working")
        else:
            print("✗ Password verification failed")
            return False
        
        # Test login with default admin
        result = auth_manager.login("admin", "admin123")
        if result['success']:
            print("✓ Default admin login successful")
            auth_manager.logout()
        else:
            print(f"✗ Default admin login failed: {result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Authentication test failed: {e}")
        return False

def test_localization():
    """Test localization system"""
    print("\nTesting localization...")
    
    try:
        from core.localization import localization_manager, _
        
        # Test English
        localization_manager.set_language("en")
        english_text = _("login.title", "Login")
        print(f"✓ English localization: '{english_text}'")
        
        # Test Arabic
        localization_manager.set_language("ar")
        arabic_text = _("login.title", "تسجيل الدخول")
        print(f"✓ Arabic localization: '{arabic_text}'")
        
        # Test RTL detection
        if localization_manager.is_rtl():
            print("✓ RTL detection working for Arabic")
        else:
            print("✗ RTL detection failed for Arabic")
            return False
        
        # Reset to English
        localization_manager.set_language("en")
        
        return True
        
    except Exception as e:
        print(f"✗ Localization test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        from gui.login_window import LoginWindow
        from gui.main_window import MainWindow

        # Create login window (don't show it)
        login_window = LoginWindow()
        print("✓ Login window can be created")

        # Create main window (don't show it)
        dummy_user = {
            'first_name': 'Test',
            'last_name': 'User'
        }
        main_window = MainWindow(dummy_user)
        print("✓ Main window can be created")

        return True
        
    except Exception as e:
        print(f"✗ GUI components test failed: {e}")
        return False

def test_file_structure():
    """Test file structure"""
    print("\nTesting file structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        "config/settings.py",
        "config/database.py",
        "core/auth.py",
        "core/database_manager.py",
        "core/localization.py",
        "gui/login_window.py",
        "localization/en.json",
        "localization/ar.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (project_root / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"✗ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✓ All required files present")
        return True

def main():
    """Run all tests"""
    print("Business Management System - Setup Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Database Setup", test_database_setup),
        ("Authentication", test_authentication),
        ("Localization", test_localization),
        ("GUI Components", test_gui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name} Test:")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} test PASSED")
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("\nTo start the application, run:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nMake sure you have installed all requirements:")
        print("pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
