"""
Business Management System - Main Application Entry Point
"""
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import customtkinter as ctk
from tkinter import messagebox
import threading

# Import core modules
from config.database import initialize_database
from config.settings import settings
from core.auth import auth_manager
from core.localization import localization_manager, _
from gui.login_window import LoginWindow

class BusinessManagementApp:
    """Main application class"""
    
    def __init__(self):
        self.login_window = None
        self.main_window = None
        self.current_user = None
        
        # Initialize application
        self.initialize_app()
    
    def initialize_app(self):
        """Initialize the application"""
        try:
            print("Initializing Business Management System...")
            
            # Initialize database
            print("Setting up database...")
            initialize_database()
            
            # Load localization
            print("Loading localization...")
            localization_manager.load_translations()
            
            print("Application initialized successfully!")
            
        except Exception as e:
            print(f"Error initializing application: {e}")
            messagebox.showerror(
                "Initialization Error",
                f"Failed to initialize application:\n{str(e)}"
            )
            sys.exit(1)
    
    def show_login(self):
        """Show the login window"""
        try:
            self.login_window = LoginWindow()
            self.login_window.create_window(self.on_login_success)
            self.login_window.show()
            
        except Exception as e:
            print(f"Error showing login window: {e}")
            messagebox.showerror(
                "Error",
                f"Failed to show login window:\n{str(e)}"
            )
    
    def on_login_success(self, login_result):
        """Handle successful login"""
        try:
            self.current_user = login_result.get('user')
            print(f"User logged in: {self.current_user['username']}")
            
            # Show main application window
            self.show_main_window()
            
        except Exception as e:
            print(f"Error handling login success: {e}")
            messagebox.showerror(
                "Error",
                f"Failed to proceed after login:\n{str(e)}"
            )
    
    def show_main_window(self):
        """Show the main application window"""
        try:
            # For now, show a simple main window
            # This will be replaced with the full main window implementation
            self.main_window = ctk.CTk()
            self.main_window.title(_("app.title", "Business Management System"))
            self.main_window.geometry("1200x800")
            
            # Welcome message
            welcome_frame = ctk.CTkFrame(self.main_window)
            welcome_frame.pack(fill="both", expand=True, padx=20, pady=20)
            
            welcome_label = ctk.CTkLabel(
                welcome_frame,
                text=f"{_('login.welcome', 'Welcome back!')}\n{self.current_user['first_name']} {self.current_user['last_name']}",
                font=ctk.CTkFont(size=24, weight="bold")
            )
            welcome_label.pack(expand=True)
            
            # Logout button
            logout_button = ctk.CTkButton(
                welcome_frame,
                text=_("navigation.logout", "Logout"),
                command=self.logout,
                font=ctk.CTkFont(size=14)
            )
            logout_button.pack(pady=20)
            
            # Handle window close
            self.main_window.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            self.main_window.mainloop()
            
        except Exception as e:
            print(f"Error showing main window: {e}")
            messagebox.showerror(
                "Error",
                f"Failed to show main window:\n{str(e)}"
            )
    
    def logout(self):
        """Handle user logout"""
        try:
            # Confirm logout
            if messagebox.askyesno(
                _("navigation.logout", "Logout"),
                "Are you sure you want to logout?"
            ):
                # Logout user
                auth_manager.logout()
                self.current_user = None
                
                # Close main window
                if self.main_window:
                    self.main_window.destroy()
                    self.main_window = None
                
                # Show login window again
                self.show_login()
                
        except Exception as e:
            print(f"Error during logout: {e}")
            messagebox.showerror(
                "Error",
                f"Failed to logout:\n{str(e)}"
            )
    
    def on_closing(self):
        """Handle application closing"""
        try:
            if messagebox.askyesno(
                "Exit Application",
                "Are you sure you want to exit?"
            ):
                # Logout user if logged in
                if auth_manager.is_authenticated():
                    auth_manager.logout()
                
                # Close database connections
                from core.database_manager import db_manager
                db_manager.close_connection()
                
                # Destroy windows
                if self.main_window:
                    self.main_window.destroy()
                
                if self.login_window:
                    self.login_window.destroy()
                
                sys.exit(0)
                
        except Exception as e:
            print(f"Error during application closing: {e}")
            sys.exit(1)
    
    def run(self):
        """Run the application"""
        try:
            # Show login window
            self.show_login()
            
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            sys.exit(0)
        except Exception as e:
            print(f"Unexpected error: {e}")
            messagebox.showerror(
                "Unexpected Error",
                f"An unexpected error occurred:\n{str(e)}"
            )
            sys.exit(1)

def main():
    """Main entry point"""
    try:
        # Create and run the application
        app = BusinessManagementApp()
        app.run()
        
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
