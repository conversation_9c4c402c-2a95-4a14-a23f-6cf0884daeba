"""
Authentication and Session Management
"""
import bcrypt
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from core.database_manager import db_manager

class AuthenticationManager:
    """Handles user authentication and session management"""
    
    def __init__(self):
        self.current_user = None
        self.current_session = None
        self.login_attempts = {}
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user and create session"""
        # Check for too many failed attempts
        if self._is_account_locked(username):
            return {
                'success': False,
                'message': 'Account temporarily locked due to too many failed attempts'
            }
        
        # Get user from database
        user = self._get_user_by_username(username)
        if not user:
            self._record_failed_attempt(username)
            return {
                'success': False,
                'message': 'Invalid username or password'
            }
        
        # Check if user is active
        if not user['is_active']:
            return {
                'success': False,
                'message': 'Account is disabled'
            }
        
        # Verify password
        if not self.verify_password(password, user['password_hash']):
            self._record_failed_attempt(username)
            return {
                'success': False,
                'message': 'Invalid username or password'
            }
        
        # Clear failed attempts
        if username in self.login_attempts:
            del self.login_attempts[username]
        
        # Update last login
        db_manager.update_record(
            'users',
            {'last_login': datetime.now().isoformat()},
            'id = ?',
            (user['id'],)
        )
        
        # Create session
        self.current_user = user
        self.current_session = {
            'user_id': user['id'],
            'username': user['username'],
            'role_id': user['role_id'],
            'company_id': user['company_id'],
            'branch_id': user['branch_id'],
            'login_time': datetime.now(),
            'last_activity': datetime.now()
        }
        
        return {
            'success': True,
            'message': 'Login successful',
            'user': self._get_user_info(user)
        }
    
    def logout(self):
        """End current session"""
        self.current_user = None
        self.current_session = None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        if not self.current_session:
            return False
        
        # Check session timeout
        last_activity = self.current_session['last_activity']
        timeout = timedelta(seconds=3600)  # 1 hour
        
        if datetime.now() - last_activity > timeout:
            self.logout()
            return False
        
        # Update last activity
        self.current_session['last_activity'] = datetime.now()
        return True
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """Get current authenticated user"""
        if self.is_authenticated():
            return self.current_user
        return None
    
    def get_current_session(self) -> Optional[Dict[str, Any]]:
        """Get current session info"""
        if self.is_authenticated():
            return self.current_session
        return None
    
    def change_password(self, old_password: str, new_password: str) -> Dict[str, Any]:
        """Change user password"""
        if not self.is_authenticated():
            return {'success': False, 'message': 'Not authenticated'}
        
        user = self.current_user
        
        # Verify old password
        if not self.verify_password(old_password, user['password_hash']):
            return {'success': False, 'message': 'Current password is incorrect'}
        
        # Validate new password
        if len(new_password) < 8:
            return {'success': False, 'message': 'Password must be at least 8 characters long'}
        
        # Hash new password
        new_hash = self.hash_password(new_password)
        
        # Update in database
        db_manager.update_record(
            'users',
            {'password_hash': new_hash, 'updated_at': datetime.now().isoformat()},
            'id = ?',
            (user['id'],)
        )
        
        # Update current user
        self.current_user['password_hash'] = new_hash
        
        return {'success': True, 'message': 'Password changed successfully'}
    
    def _get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        users = db_manager.execute_query(
            """
            SELECT u.*, r.name as role_name, r.permissions, c.name as company_name
            FROM users u
            JOIN roles r ON u.role_id = r.id
            JOIN companies c ON u.company_id = c.id
            WHERE u.username = ?
            """,
            (username,)
        )
        return users[0] if users else None
    
    def _get_user_info(self, user: Dict[str, Any]) -> Dict[str, Any]:
        """Get sanitized user info for session"""
        return {
            'id': user['id'],
            'username': user['username'],
            'email': user['email'],
            'first_name': user['first_name'],
            'last_name': user['last_name'],
            'role_name': user['role_name'],
            'company_name': user['company_name'],
            'company_id': user['company_id'],
            'branch_id': user['branch_id']
        }
    
    def _record_failed_attempt(self, username: str):
        """Record a failed login attempt"""
        if username not in self.login_attempts:
            self.login_attempts[username] = []
        
        self.login_attempts[username].append(time.time())
        
        # Keep only recent attempts (last hour)
        cutoff = time.time() - 3600
        self.login_attempts[username] = [
            attempt for attempt in self.login_attempts[username]
            if attempt > cutoff
        ]
    
    def _is_account_locked(self, username: str) -> bool:
        """Check if account is locked due to failed attempts"""
        if username not in self.login_attempts:
            return False
        
        # Check attempts in last 15 minutes
        cutoff = time.time() - 900  # 15 minutes
        recent_attempts = [
            attempt for attempt in self.login_attempts[username]
            if attempt > cutoff
        ]
        
        return len(recent_attempts) >= 3

# Global authentication manager instance
auth_manager = AuthenticationManager()
