# Business Management System - Installation Guide

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: Version 3.9 or higher
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 500 MB free space for application and database
- **Display**: 1024x768 minimum resolution, 1920x1080 recommended

### Recommended Requirements
- **Python**: Version 3.11 or higher
- **RAM**: 8 GB or more
- **Storage**: 2 GB free space
- **Display**: 1920x1080 or higher resolution

## Installation Steps

### Step 1: Install Python
1. Download Python from [python.org](https://www.python.org/downloads/)
2. During installation, make sure to check "Add Python to PATH"
3. Verify installation by opening command prompt/terminal and running:
   ```bash
   python --version
   ```

### Step 2: Download the Application
1. Download or clone the Business Management System files
2. Extract to a folder (e.g., `C:\BusinessManagement` or `~/BusinessManagement`)

### Step 3: Install Dependencies
1. Open command prompt/terminal in the application folder
2. Create a virtual environment (recommended):
   ```bash
   python -m venv venv
   ```

3. Activate the virtual environment:
   - **Windows**:
     ```bash
     venv\Scripts\activate
     ```
   - **macOS/Linux**:
     ```bash
     source venv/bin/activate
     ```

4. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```

### Step 4: Verify Installation
Run the setup test to ensure everything is working correctly:
```bash
python test_setup.py
```

You should see all tests pass with the message:
```
🎉 All tests passed! The application is ready to run.
```

### Step 5: Run the Application
Start the application:
```bash
python main.py
```

## Default Login Credentials

- **Username**: `admin`
- **Password**: `admin123`

**⚠️ IMPORTANT**: Change the default password immediately after first login for security.

## Troubleshooting

### Common Issues

#### 1. Python Not Found
**Error**: `'python' is not recognized as an internal or external command`
**Solution**: 
- Reinstall Python and ensure "Add Python to PATH" is checked
- Try using `python3` instead of `python`

#### 2. Package Installation Fails
**Error**: Various pip installation errors
**Solutions**:
- Update pip: `python -m pip install --upgrade pip`
- Use virtual environment (recommended)
- On Windows, try: `pip install --user -r requirements.txt`

#### 3. GUI Not Displaying
**Error**: Application starts but no window appears
**Solutions**:
- Ensure you have a display/desktop environment
- On Linux, install tkinter: `sudo apt-get install python3-tk`
- Check if running in a virtual environment without GUI support

#### 4. Database Errors
**Error**: Database connection or initialization fails
**Solutions**:
- Ensure the `data` folder has write permissions
- Delete `data/database.db` and restart the application
- Run `python test_setup.py` to verify database setup

#### 5. Arabic Text Not Displaying Correctly
**Error**: Arabic text appears as squares or incorrect characters
**Solutions**:
- Ensure Arabic fonts are installed on your system
- Update the `arabic-reshaper` and `python-bidi` packages
- Restart the application after font installation

### Performance Issues

#### Slow Startup
- Check antivirus software (may scan Python files)
- Ensure sufficient RAM is available
- Close unnecessary applications

#### Database Performance
- Regular database maintenance (backup and restore)
- Monitor database file size
- Consider periodic cleanup of old records

## File Structure After Installation

```
business_management_system/
├── main.py                    # Main application
├── test_setup.py             # Installation test
├── requirements.txt          # Dependencies
├── README.md                 # Documentation
├── INSTALLATION_GUIDE.md     # This file
├── PROJECT_PLAN.md          # Technical documentation
├── config/                   # Configuration
├── core/                     # Core functionality
├── gui/                      # User interface
├── localization/             # Language files
├── data/                     # Database and user data
│   ├── database.db          # Main database (created on first run)
│   ├── backups/             # Backup files
│   └── uploads/             # Uploaded files
└── venv/                     # Virtual environment (if created)
```

## Security Considerations

### Initial Setup
1. Change default admin password immediately
2. Create individual user accounts for each person
3. Assign appropriate roles and permissions
4. Enable regular backups

### Ongoing Security
1. Regular password updates
2. Monitor user activity logs
3. Keep the application updated
4. Secure the database file
5. Regular security audits

## Backup and Recovery

### Automatic Backups
The application includes built-in backup functionality:
- Scheduled automatic backups
- Manual backup creation
- Backup verification

### Manual Backup
To manually backup your data:
1. Copy the entire `data` folder
2. Store in a secure location
3. Test restore procedure periodically

### Recovery
To restore from backup:
1. Stop the application
2. Replace the `data` folder with backup
3. Restart the application

## Support and Updates

### Getting Help
1. Check this installation guide
2. Review the troubleshooting section
3. Run `python test_setup.py` to diagnose issues
4. Contact technical support with error details

### Updates
- Check for application updates regularly
- Always backup data before updating
- Test updates in a separate environment first

## Uninstallation

To remove the application:
1. Backup your data if needed
2. Deactivate virtual environment: `deactivate`
3. Delete the application folder
4. Remove Python if no longer needed

---

**Note**: This installation guide covers the basic setup. For advanced configuration and customization options, refer to the technical documentation in `PROJECT_PLAN.md`.
