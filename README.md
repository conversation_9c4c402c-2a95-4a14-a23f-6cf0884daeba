# Business Management System

A comprehensive integrated business management application built with Python and SQLite3, featuring bilingual support (Arabic/English) and a modern GUI interface.

## Features

- **Multi-language Support**: Arabic and English with RTL/LTR layout support
- **User Authentication**: Secure login with role-based permissions
- **Customer Management**: Complete customer database and relationship management
- **Supplier Management**: Supplier information and transaction tracking
- **Inventory Management**: Product catalog, stock tracking, and inventory control
- **Purchase Management**: Purchase orders and supplier transactions
- **Sales Management**: Sales orders and customer transactions
- **Invoice Management**: Invoice generation, PDF creation, and archiving
- **General Ledger**: Complete accounting system with chart of accounts
- **Financial Reports**: Profit & Loss, Balance Sheet, Tax Reports
- **Employee Management**: HR functionality and employee records
- **Multi-branch/Company Support**: Data segregation for multiple entities
- **Notification System**: Real-time notifications and alerts
- **Activity Log**: Comprehensive audit trail
- **Backup & Restore**: Automated data backup and recovery
- **Dashboard**: Business intelligence and key performance indicators

## Technical Stack

- **Backend**: Python 3.9+
- **Database**: SQLite3
- **GUI Framework**: CustomTkinter (modern Tkinter)
- **Additional Libraries**:
  - Pillow (Image processing)
  - ReportLab (PDF generation)
  - Matplotlib (Charts and graphs)
  - Pandas (Data analysis)
  - bcrypt (Password hashing)
  - python-bidi (Arabic text support)
  - arabic-reshaper (Arabic text reshaping)

## Installation

### Prerequisites

- Python 3.9 or higher
- pip (Python package installer)

### Setup Instructions

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd business_management_system
   ```

2. **Create a virtual environment** (recommended):
   ```bash
   python -m venv venv
   
   # On Windows:
   venv\Scripts\activate
   
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install required packages**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

## Default Login Credentials

- **Username**: admin
- **Password**: admin123

**Important**: Change the default password after first login for security.

## Project Structure

```
business_management_system/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── PROJECT_PLAN.md        # Detailed project documentation
├── config/                # Configuration files
│   ├── settings.py        # Application settings
│   └── database.py        # Database schema and setup
├── core/                  # Core functionality
│   ├── auth.py           # Authentication system
│   ├── database_manager.py # Database operations
│   └── localization.py   # Multi-language support
├── gui/                   # User interface
│   └── login_window.py    # Login interface
├── localization/          # Translation files
│   ├── en.json           # English translations
│   └── ar.json           # Arabic translations
├── modules/               # Business modules (to be implemented)
├── data/                  # Database and user data
└── assets/                # Images, icons, and themes
```

## Development Status

This is the initial foundation of the Business Management System. The following components are currently implemented:

✅ **Completed**:
- Project architecture and structure
- Database schema design
- Authentication system
- Multi-language support (Arabic/English)
- Login interface
- Basic application framework

🚧 **In Progress**:
- Database implementation and initialization
- Core business modules
- Main application interface

📋 **Planned**:
- Customer/Supplier management
- Inventory management
- Sales and purchase modules
- Financial reporting
- Dashboard and analytics

## Configuration

The application can be configured through the `config/settings.py` file:

- **Database location**: `data/database.db`
- **Default language**: English (can be changed to Arabic)
- **Session timeout**: 1 hour
- **Window size**: 1200x800 (minimum 800x600)
- **Theme**: Light mode (dark mode support planned)

## Security Features

- **Password Hashing**: bcrypt encryption for secure password storage
- **Session Management**: Automatic session timeout and validation
- **Account Lockout**: Protection against brute force attacks
- **Role-based Access**: Different permission levels for users
- **Audit Logging**: Complete activity tracking

## Localization

The application supports both Arabic and English languages:

- **Dynamic Language Switching**: Change language without restart
- **RTL Support**: Proper right-to-left layout for Arabic
- **Text Reshaping**: Correct Arabic text rendering
- **Localized Formatting**: Numbers, dates, and currency

## Contributing

This project is designed with modularity in mind. Each business module is self-contained and can be developed independently. See `PROJECT_PLAN.md` for detailed development phases and architecture.

## License

This project is proprietary software. All rights reserved.

## Support

For technical support or questions about the system, please contact the development team.

---

**Note**: This is a comprehensive business management system designed for professional use. Ensure proper backup procedures and security measures are in place before deploying in a production environment.
